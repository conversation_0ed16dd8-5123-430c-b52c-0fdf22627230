# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*
!.env.example

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts



# production
.next/
out/
dist/

# logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# runtime data
pids
*.pid
*.seed
*.pid.lock

# coverage directory used by tools like istanbul
coverage/

# dependency directories
node_modules/

# optional npm cache directory
.npm

# optional eslint cache
.eslintcache
