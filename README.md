# <PERSON><PERSON>mat - Portfolio Website

A modern, fully responsive portfolio website built with the latest web technologies. This portfolio showcases a full-stack developer's skills, projects, and experience with beautiful animations and interactive features.

## 🚀 Features

- **Modern Tech Stack**: Built with Next.js 15, React 19, TypeScript, and Tailwind CSS v4
- **Fully Responsive**: Optimized for all screen sizes from mobile to desktop
- **Dark/Light Theme**: Seamless theme switching with system preference detection
- **Interactive Animations**: Smooth animations using Framer Motion
- **Command Palette**: Quick navigation with keyboard shortcuts (⌘K)
- **SEO Optimized**: Comprehensive meta tags, sitemap, and robots.txt
- **Performance Focused**: Optimized images, lazy loading, and fast loading times
- **Accessible**: ARIA labels, keyboard navigation, and screen reader friendly
- **Contact Form**: Functional contact form with validation
- **PWA Ready**: Manifest file and service worker support

## 🛠️ Tech Stack

### Frontend
- **Next.js 15** - React framework with App Router
- **React 19** - Latest React features and concurrent rendering
- **TypeScript** - Type safety and better developer experience
- **Tailwind CSS v4** - Utility-first CSS framework
- **Framer Motion** - Smooth animations and transitions
- **Shadcn/ui** - Beautiful and accessible UI components

### Development Tools
- **ESLint** - Code linting and quality
- **Prettier** - Code formatting
- **Husky** - Git hooks for code quality
- **React Hook Form** - Form handling and validation
- **Zod** - Schema validation
- **Lucide React** - Beautiful icons

## 📱 Sections

1. **Hero Section** - Animated introduction with typing effect
2. **Tech Stack** - Scrolling technology showcase
3. **Services** - Interactive service cards
4. **Projects** - Featured projects with filtering
5. **Testimonials** - Client testimonials carousel
6. **Contact** - Functional contact form
7. **Footer** - Links and additional information

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. Clone the repository:
```bash
git clone https://github.com/Ash-333/portfolio-frontend
cd portfolio
```

2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Run the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 🎨 Customization

### Personal Information
Update the following files with your information:
- `src/components/hero-section.tsx` - Name, roles, and social links
- `src/components/tech-stack.tsx` - Your technology stack
- `src/components/services.tsx` - Your services and expertise
- `src/components/projects-showcase.tsx` - Your projects (replace dummy data)
- `src/components/testimonials.tsx` - Client testimonials
- `src/components/contact-section.tsx` - Contact information
- `src/app/layout.tsx` - SEO metadata

### Styling
- Colors and themes: `src/app/globals.css`
- Component styles: Individual component files
- Tailwind config: `tailwind.config.js`

### Images
Replace placeholder images in the `public` folder:
- `ashish-profile.svg` - Your profile picture
- `icon-192x192.png` - PWA icon (192x192)
- `icon-512x512.png` - PWA icon (512x512)

## 📦 Build and Deploy

### Build for Production
```bash
npm run build
```

### Deploy to Vercel
1. Push your code to GitHub
2. Connect your repository to [Vercel](https://vercel.com)
3. Deploy automatically on every push

### Deploy to Other Platforms
The built application in the `.next` folder can be deployed to any platform that supports Node.js.

## 🎯 Performance

- **Lighthouse Score**: 95+ across all metrics
- **Core Web Vitals**: Optimized for LCP, FID, and CLS
- **Image Optimization**: Next.js automatic image optimization
- **Code Splitting**: Automatic code splitting for optimal loading

## 🔧 Development

### Available Scripts
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript checks

### Project Structure
```
src/
├── app/                 # Next.js App Router
├── components/          # React components
│   ├── ui/             # Shadcn/ui components
│   └── ...             # Custom components
├── lib/                # Utility functions
└── styles/             # Global styles

public/                 # Static assets
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org) - The React framework
- [Tailwind CSS](https://tailwindcss.com) - CSS framework
- [Framer Motion](https://framer.com/motion) - Animation library
- [Shadcn/ui](https://ui.shadcn.com) - UI components
- [Lucide](https://lucide.dev) - Icon library

---

Built with ❤️ by [Ashish Kamat](https://ashishkamat.com.np)
