"use client";

import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { Card, CardContent } from "@/components/ui/card";

const stats = [
  {
    number: "50+",
    label: "Projects Completed",
    description: "From small websites to complex web applications"
  },
  {
    number: "20+",
    label: "Happy Clients",
    description: "Satisfied clients across various industries"
  },
  {
    number: "15+",
    label: "Technologies",
    description: "Modern tools and frameworks mastered"
  },
  {
    number: "3+",
    label: "Years Experience",
    description: "Professional development experience"
  }
];

export function ProjectsStats() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  return (
    <section ref={ref} className="py-20">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, scale: 0.5 }}
              animate={inView ? { opacity: 1, scale: 1 } : {}}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <Card className="text-center hover-lift border-border/50 hover:border-border transition-all duration-300">
                <CardContent className="p-6">
                  <div className="text-3xl sm:text-4xl font-bold gradient-text-blue mb-2">
                    {stat.number}
                  </div>
                  <div className="font-semibold mb-1">{stat.label}</div>
                  <div className="text-xs text-muted-foreground">{stat.description}</div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
