"use client";

import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { GraduationCap, Award, BookOpen, School, Trophy } from "lucide-react";
import { VerticalTimeline, VerticalTimelineElement } from 'react-vertical-timeline-component';
import 'react-vertical-timeline-component/style.min.css';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { api, getPublishedEducation, getPublishedCertifications, type Education, type Certification } from "@/lib/api";
import { useEffect, useState } from "react";

// Fallback education data
const fallbackEducation = [
  {
    degree: "Bachelor of Engineering in Computer Science",
    institution: "Chandigarh University",
    location: "Chandigarh, India",
    period: "2020 - 2024",
    grade: "First Class with Distinction (8.5/10 CGPA)",
    description: "Focused on software engineering, data structures, algorithms, and web technologies. Completed final year project on e-commerce platform development.",
    highlights: [
      "Dean's List for 3 consecutive semesters",
      "Led university coding club with 200+ members",
      "Won inter-college hackathon for innovative web solution",
      "Published research paper on web performance optimization"
    ],
    icon: GraduationCap,
    iconBg: "#3b82f6",
    date: "2016 - 2020"
  },
  {
    degree: "Higher Secondary Certificate (Science)",
    institution: "St. Xavier's College",
    location: "Mumbai, India",
    period: "2014 - 2016",
    grade: "92.5%",
    description: "Specialized in Mathematics, Physics, and Chemistry with additional focus on computer science fundamentals.",
    highlights: [
      "School topper in Computer Science",
      "Represented school in state-level science exhibition",
      "Active member of robotics club"
    ],
    icon: School,
    iconBg: "#10b981",
    date: "2014 - 2016"
  }
];

const fallbackCertifications = [
  {
    title: "AWS Certified Solutions Architect",
    issuer: "Amazon Web Services",
    date: "2023",
    credentialId: "AWS-CSA-2023-001",
    emoji: "☁️",
    icon: Award,
    iconBg: "#f59e0b",
    description: "Comprehensive certification covering AWS architecture best practices, security, and scalability."
  },
  {
    title: "React Developer Certification",
    issuer: "Meta (Facebook)",
    date: "2022",
    credentialId: "META-REACT-2022-456",
    emoji: "⚛️",
    icon: Trophy,
    iconBg: "#3b82f6",
    description: "Advanced React development patterns, hooks, and modern React ecosystem."
  },
  {
    title: "Google Analytics Certified",
    issuer: "Google",
    date: "2022",
    credentialId: "GOOGLE-GA-2022-789",
    emoji: "📊",
    icon: Award,
    iconBg: "#10b981",
    description: "Digital analytics, data interpretation, and conversion optimization strategies."
  },
  {
    title: "MongoDB Developer Certification",
    issuer: "MongoDB University",
    date: "2021",
    credentialId: "MONGO-DEV-2021-123",
    emoji: "🍃",
    icon: Trophy,
    iconBg: "#8b5cf6",
    description: "NoSQL database design, aggregation pipelines, and performance optimization."
  }
];

const courses = [
  "Advanced React Patterns - Kent C. Dodds",
  "TypeScript Masterclass - Marius Schulz",
  "Node.js Design Patterns - Mario Casciaro",
  "System Design Interview - Alex Xu",
  "AWS Solutions Architecture - A Cloud Guru",
  "UI/UX Design Fundamentals - Google UX"
];

export function Education() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [education, setEducation] = useState<Education[]>([]);
  const [certifications, setCertifications] = useState<Certification[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [educationData, certificationsData] = await Promise.all([
          api.getEducation(),
          api.getCertifications()
        ]);

        setEducation(getPublishedEducation(educationData));
        setCertifications(getPublishedCertifications(certificationsData));
      } catch (error) {
        console.error('Failed to fetch education data:', error);
        // Use fallback data if API fails
        setEducation(fallbackEducation.map((edu, index) => ({
          id: `fallback-edu-${index}`,
          degree: edu.degree,
          institution: edu.institution,
          location: edu.location,
          period: edu.period,
          grade: edu.grade,
          description: edu.description,
          highlights: edu.highlights,
          order: index,
          published: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        })));

        setCertifications(fallbackCertifications.map((cert, index) => ({
          id: `fallback-cert-${index}`,
          title: cert.title,
          issuer: cert.issuer,
          date: cert.date,
          credentialId: cert.credentialId,
          emoji: cert.emoji,
          description: cert.description,
          order: index,
          published: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        })));
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <section className="py-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-300 rounded w-64 mx-auto mb-4"></div>
              <div className="h-4 bg-gray-300 rounded w-96 mx-auto"></div>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section ref={ref} className="py-20">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 50 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
        >
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-6">
            <span className="gradient-text">Education & Learning</span>
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            My educational background and continuous learning journey in technology and software development.
          </p>
        </motion.div>

        {/* Formal Education */}
        <div className="mb-16">
          <motion.h3
            className="text-2xl font-bold mb-8 flex items-center"
            initial={{ opacity: 0, x: -20 }}
            animate={inView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <GraduationCap className="mr-3 h-6 w-6 text-primary" />
            Formal Education
          </motion.h3>

          <motion.div
            initial={{ opacity: 0 }}
            animate={inView ? { opacity: 1 } : {}}
            transition={{ duration: 0.8, delay: 0.3 }}
          >
            <VerticalTimeline>
              {education.map((edu) => {
                return (
                  <VerticalTimelineElement
                    key={edu.id}
                    className="vertical-timeline-element--education"
                    contentStyle={{
                      background: 'hsl(var(--card))',
                      color: 'hsl(var(--card-foreground))',
                      border: '1px solid hsl(var(--border))',
                      borderRadius: '12px',
                      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                    }}
                    contentArrowStyle={{
                      borderRight: '7px solid hsl(var(--border))',
                    }}
                    date={edu.period}
                    iconStyle={{
                      background: '#3b82f6',
                      color: '#fff',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      border: '3px solid hsl(var(--background))',
                      boxShadow: '0 0 0 2px hsl(var(--border))',
                      width: '60px',
                      height: '60px',
                    }}
                    icon={<GraduationCap className="h-7 w-7" />}
                  >
                    <div className="space-y-4">
                      <div>
                        <h3 className="text-xl font-bold">{edu.degree}</h3>
                        <div className="text-primary font-medium">{edu.institution}</div>
                        <div className="text-sm text-muted-foreground">{edu.location}</div>
                        <Badge variant="secondary" className="mt-2">{edu.grade}</Badge>
                      </div>

                      <p className="text-muted-foreground">{edu.description}</p>

                      <div>
                        <h4 className="font-semibold mb-2">Key Highlights:</h4>
                        <ul className="space-y-1">
                          {edu.highlights.map((highlight, highlightIndex) => (
                            <li key={highlightIndex} className="flex items-start space-x-2 text-sm text-muted-foreground">
                              <div className="w-1.5 h-1.5 rounded-full bg-primary mt-2 flex-shrink-0" />
                              <span>{highlight}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </VerticalTimelineElement>
                );
              })}
            </VerticalTimeline>
          </motion.div>
        </div>

        {/* Certifications */}
        <div className="mb-16">
          <motion.h3
            className="text-2xl font-bold mb-8 flex items-center"
            initial={{ opacity: 0, x: -20 }}
            animate={inView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <Award className="mr-3 h-6 w-6 text-primary" />
            Certifications
          </motion.h3>

          <motion.div
            initial={{ opacity: 0 }}
            animate={inView ? { opacity: 1 } : {}}
            transition={{ duration: 0.8, delay: 0.5 }}
          >
            <VerticalTimeline>
              {certifications.map((cert) => {
                return (
                  <VerticalTimelineElement
                    key={cert.id}
                    className="vertical-timeline-element--certification"
                    contentStyle={{
                      background: 'hsl(var(--card))',
                      color: 'hsl(var(--card-foreground))',
                      border: '1px solid hsl(var(--border))',
                      borderRadius: '12px',
                      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                    }}
                    contentArrowStyle={{
                      borderRight: '7px solid hsl(var(--border))',
                    }}
                    date={cert.date}
                    iconStyle={{
                      background: '#f59e0b',
                      color: '#fff',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      border: '3px solid hsl(var(--background))',
                      boxShadow: '0 0 0 2px hsl(var(--border))',
                      width: '60px',
                      height: '60px',
                    }}
                    icon={<Award className="h-6 w-6" />}
                  >
                    <div className="space-y-3">
                      <div className="flex items-start space-x-3">
                        <div className="text-2xl">{cert.emoji}</div>
                        <div className="flex-1">
                          <h4 className="font-bold text-lg">{cert.title}</h4>
                          <p className="text-primary font-medium">{cert.issuer}</p>
                          <p className="text-sm text-muted-foreground mt-1">{cert.description}</p>
                          <p className="text-xs text-muted-foreground mt-2">
                            Credential ID: {cert.credentialId}
                          </p>
                        </div>
                      </div>
                    </div>
                  </VerticalTimelineElement>
                );
              })}
            </VerticalTimeline>
          </motion.div>
        </div>

        {/* Continuous Learning */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          <h3 className="text-2xl font-bold mb-8 flex items-center">
            <BookOpen className="mr-3 h-6 w-6 text-primary" />
            Continuous Learning
          </h3>
          
          <Card className="border-border/50">
            <CardHeader>
              <CardTitle>Recent Courses & Training</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-4">
                {courses.map((course, index) => (
                  <motion.div
                    key={index}
                    className="flex items-center space-x-3 p-3 rounded-lg bg-muted/50 hover:bg-muted transition-colors duration-200"
                    initial={{ opacity: 0, x: -20 }}
                    animate={inView ? { opacity: 1, x: 0 } : {}}
                    transition={{ duration: 0.4, delay: 0.7 + index * 0.05 }}
                  >
                    <div className="w-2 h-2 rounded-full bg-primary flex-shrink-0" />
                    <span className="text-sm font-medium">{course}</span>
                  </motion.div>
                ))}
              </div>
              
              <div className="mt-6 p-4 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg border border-border/50">
                <p className="text-sm text-muted-foreground">
                  <strong>Learning Philosophy:</strong> I believe in continuous learning and staying updated with the latest 
                  technologies. I dedicate at least 5 hours per week to learning new skills and exploring emerging trends 
                  in web development and software engineering.
                </p>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  );
}
