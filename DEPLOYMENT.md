# Vercel Deployment Guide

## Prerequisites
1. GitHub repository with your code
2. Vercel account (free tier available)
3. Production database (Supabase/Neon recommended)

## Environment Variables for Vercel

### Required Environment Variables
Set these in your Vercel dashboard under Project Settings > Environment Variables:

```bash
# Database (Required)
DATABASE_URL="postgresql://username:password@host:port/database"

# NextAuth.js (Required if using authentication)
NEXTAUTH_URL="https://your-domain.vercel.app"
NEXTAUTH_SECRET="your-super-secret-key-min-32-chars"

# OAuth Providers (if using)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
GITHUB_CLIENT_ID="your-github-client-id"
GITHUB_CLIENT_SECRET="your-github-client-secret"

# Google Analytics (if using)
GOOGLE_ANALYTICS_PROPERTY_ID="your-ga-property-id"

# Email Service (if using contact form)
RESEND_API_KEY="your-resend-api-key"
# OR
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# Cloudinary (if using for image uploads)
CLOUDINARY_CLOUD_NAME="your-cloud-name"
CLOUDINARY_API_KEY="your-api-key"
CLOUDINARY_API_SECRET="your-api-secret"
```

## Deployment Steps

### 1. Database Setup (Choose one)

#### Option A: Supabase (Recommended)
1. Go to [supabase.com](https://supabase.com)
2. Create new project
3. Get connection string from Settings > Database
4. Format: `postgresql://postgres:[password]@[host]:5432/postgres`

#### Option B: Neon
1. Go to [neon.tech](https://neon.tech)
2. Create new project
3. Get connection string from dashboard

### 2. Vercel Deployment
1. Go to [vercel.com](https://vercel.com)
2. Import your GitHub repository
3. Configure environment variables
4. Deploy!

### 3. Post-Deployment
1. Run database migrations (automatic with vercel-build script)
2. Test all functionality
3. Set up custom domain (optional)

## Build Configuration
- Build Command: `npm run vercel-build`
- Output Directory: `.next`
- Install Command: `npm install`
- Development Command: `npm run dev`

## Troubleshooting

### Common Issues:
1. **Prisma Client Error**: Make sure `prisma generate` runs in build
2. **Environment Variables**: Double-check all required vars are set
3. **Database Connection**: Verify DATABASE_URL format and credentials
4. **Build Timeout**: Increase function timeout in vercel.json if needed

### Performance Tips:
1. Enable Edge Runtime for API routes when possible
2. Use Next.js Image optimization
3. Implement proper caching strategies
4. Monitor Core Web Vitals in Vercel Analytics
