"use client";

import Link from "next/link";
import { motion } from "framer-motion";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

interface BlogPostNavigationProps {
  currentSlug: string;
}

// Mock navigation data - in a real app, this would come from an API
const getNavigationPosts = (currentSlug: string) => {
  const posts = [
    {
      slug: "building-scalable-react-applications-typescript",
      title: "Building Scalable React Applications with TypeScript"
    },
    {
      slug: "nextjs-14-app-router-complete-guide",
      title: "Next.js 14 App Router: Complete Guide"
    },
    {
      slug: "mastering-css-grid-flexbox-2024",
      title: "Mastering CSS Grid and Flexbox in 2024"
    }
  ];

  const currentIndex = posts.findIndex(post => post.slug === currentSlug);
  
  return {
    previous: currentIndex > 0 ? posts[currentIndex - 1] : null,
    next: currentIndex < posts.length - 1 ? posts[currentIndex + 1] : null
  };
};

export function BlogPostNavigation({ currentSlug }: BlogPostNavigationProps) {
  const { previous, next } = getNavigationPosts(currentSlug);

  if (!previous && !next) {
    return null;
  }

  return (
    <motion.div
      className="mt-16 pt-8 border-t border-border"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <div className="grid md:grid-cols-2 gap-6">
        {/* Previous Post */}
        <div className="flex justify-start">
          {previous ? (
            <Card className="hover-lift group border-border/50 hover:border-border transition-all duration-300 w-full">
              <CardContent className="p-6">
                <Button
                  variant="ghost"
                  asChild
                  className="h-auto p-0 flex flex-col items-start space-y-2 w-full"
                >
                  <Link href={`/blog/${previous.slug}`}>
                    <div className="flex items-center text-sm text-muted-foreground group-hover:text-primary transition-colors duration-300">
                      <ChevronLeft className="mr-1 h-4 w-4" />
                      Previous Article
                    </div>
                    <h3 className="text-left font-semibold group-hover:text-primary transition-colors duration-300 line-clamp-2">
                      {previous.title}
                    </h3>
                  </Link>
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div /> // Empty div to maintain grid layout
          )}
        </div>

        {/* Next Post */}
        <div className="flex justify-end">
          {next ? (
            <Card className="hover-lift group border-border/50 hover:border-border transition-all duration-300 w-full">
              <CardContent className="p-6">
                <Button
                  variant="ghost"
                  asChild
                  className="h-auto p-0 flex flex-col items-end space-y-2 w-full"
                >
                  <Link href={`/blog/${next.slug}`}>
                    <div className="flex items-center text-sm text-muted-foreground group-hover:text-primary transition-colors duration-300">
                      Next Article
                      <ChevronRight className="ml-1 h-4 w-4" />
                    </div>
                    <h3 className="text-right font-semibold group-hover:text-primary transition-colors duration-300 line-clamp-2">
                      {next.title}
                    </h3>
                  </Link>
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div /> // Empty div to maintain grid layout
          )}
        </div>
      </div>

      {/* Back to Blog */}
      <div className="text-center mt-8">
        <Button variant="outline" asChild>
          <Link href="/blog">
            ← Back to All Articles
          </Link>
        </Button>
      </div>
    </motion.div>
  );
}
